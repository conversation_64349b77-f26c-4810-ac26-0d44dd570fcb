// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		021480F53F6A432EE8F84261 /* Pods_XYZTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 51EF4B94A8D9A973204E0320 /* Pods_XYZTests.framework */; };
		44D95136D4B6170DE7787FF6 /* Pods_XYZCommon_XYZWidgetExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 06C5B1137F4868071034C0D5 /* Pods_XYZCommon_XYZWidgetExtension.framework */; };
		9EC49C4EC383030ECDC83329 /* Pods_XYZCommon_XYZUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 895923F3BB0B06350EA49B33 /* Pods_XYZCommon_XYZUITests.framework */; };
		E440EF032D31793C00E18AB0 /* CombineCocoa in Frameworks */ = {isa = PBXBuildFile; productRef = E440EF022D31793C00E18AB0 /* CombineCocoa */; };
		E440EF062D317A6600E18AB0 /* CombineCocoa in Frameworks */ = {isa = PBXBuildFile; productRef = E440EF052D317A6600E18AB0 /* CombineCocoa */; };
		E4B2A9D12E13D76700CD6B08 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E4B2A9D02E13D76700CD6B08 /* WidgetKit.framework */; };
		E4B2A9D32E13D76700CD6B08 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E4B2A9D22E13D76700CD6B08 /* SwiftUI.framework */; };
		E4B2A9E42E13D76900CD6B08 /* XYZWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = E4B2A9CF2E13D76700CD6B08 /* XYZWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E4B919062D6EC20C004C6B74 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E4B919052D6EC20C004C6B74 /* StoreKit.framework */; };
		FB1C60BBD07AF1E3274D90BD /* Pods_XYZCommon_XYZ.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0450BDBDD4B9ABC1BEDB6252 /* Pods_XYZCommon_XYZ.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E4B2A9E22E13D76900CD6B08 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4EBBD032D2D0C81006D1114 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4B2A9CE2E13D76700CD6B08;
			remoteInfo = XYZWidgetExtension;
		};
		E4EBBD222D2D0C83006D1114 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4EBBD032D2D0C81006D1114 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4EBBD0A2D2D0C81006D1114;
			remoteInfo = XYZ;
		};
		E4EBBD2C2D2D0C83006D1114 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4EBBD032D2D0C81006D1114 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4EBBD0A2D2D0C81006D1114;
			remoteInfo = XYZ;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		E4B2A9E52E13D76900CD6B08 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				E4B2A9E42E13D76900CD6B08 /* XYZWidgetExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0450BDBDD4B9ABC1BEDB6252 /* Pods_XYZCommon_XYZ.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XYZCommon_XYZ.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		06C5B1137F4868071034C0D5 /* Pods_XYZCommon_XYZWidgetExtension.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XYZCommon_XYZWidgetExtension.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		14249290A6E0D5B41720DF05 /* Pods-XYZCommon-XYZUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZUITests.debug.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZUITests/Pods-XYZCommon-XYZUITests.debug.xcconfig"; sourceTree = "<group>"; };
		15A04F289A01ED426EC4CE4F /* Pods-XYZCommon-XYZUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZUITests.release.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZUITests/Pods-XYZCommon-XYZUITests.release.xcconfig"; sourceTree = "<group>"; };
		174FC7DDE0E41C7C90687C57 /* Pods-XYZ-XYZWidgetExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ-XYZWidgetExtension.release.xcconfig"; path = "Target Support Files/Pods-XYZ-XYZWidgetExtension/Pods-XYZ-XYZWidgetExtension.release.xcconfig"; sourceTree = "<group>"; };
		20BA8106D7C1D1F825BDF821 /* Pods-XYZ-XYZUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ-XYZUITests.release.xcconfig"; path = "Target Support Files/Pods-XYZ-XYZUITests/Pods-XYZ-XYZUITests.release.xcconfig"; sourceTree = "<group>"; };
		218D7D3F0BDE0A3F3FB30B62 /* Pods-XYZCommon-XYZ.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZ.debug.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ.debug.xcconfig"; sourceTree = "<group>"; };
		4037953381C25A069E20478C /* Pods-XYZ.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ.release.xcconfig"; path = "Target Support Files/Pods-XYZ/Pods-XYZ.release.xcconfig"; sourceTree = "<group>"; };
		51EF4B94A8D9A973204E0320 /* Pods_XYZTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XYZTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7C565C77790DADE92FDF775D /* Pods-XYZ-XYZWidgetExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ-XYZWidgetExtension.debug.xcconfig"; path = "Target Support Files/Pods-XYZ-XYZWidgetExtension/Pods-XYZ-XYZWidgetExtension.debug.xcconfig"; sourceTree = "<group>"; };
		889332147A441AA0830EC34F /* Pods-XYZTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZTests.release.xcconfig"; path = "Target Support Files/Pods-XYZTests/Pods-XYZTests.release.xcconfig"; sourceTree = "<group>"; };
		895923F3BB0B06350EA49B33 /* Pods_XYZCommon_XYZUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XYZCommon_XYZUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95EE5AC903D73E1B01C6281B /* Pods-XYZCommon-XYZWidgetExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZWidgetExtension.release.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZWidgetExtension/Pods-XYZCommon-XYZWidgetExtension.release.xcconfig"; sourceTree = "<group>"; };
		99A3E6C6B1E4F1802BBB2D3C /* Pods-XYZCommon-XYZ.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZ.release.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ.release.xcconfig"; sourceTree = "<group>"; };
		B0308D3F9A9626CEB015148A /* Pods-XYZTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZTests.debug.xcconfig"; path = "Target Support Files/Pods-XYZTests/Pods-XYZTests.debug.xcconfig"; sourceTree = "<group>"; };
		DBF9FC5589682C2C0434284C /* Pods-XYZ-XYZUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ-XYZUITests.debug.xcconfig"; path = "Target Support Files/Pods-XYZ-XYZUITests/Pods-XYZ-XYZUITests.debug.xcconfig"; sourceTree = "<group>"; };
		E4B2A9CF2E13D76700CD6B08 /* XYZWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = XYZWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		E4B2A9D02E13D76700CD6B08 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		E4B2A9D22E13D76700CD6B08 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		E4B919052D6EC20C004C6B74 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		E4EBBD0B2D2D0C81006D1114 /* XYZ.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = XYZ.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E4EBBD212D2D0C83006D1114 /* XYZTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XYZTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E4EBBD2B2D2D0C83006D1114 /* XYZUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = XYZUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E95C01900ED6E58B370920D8 /* Pods-XYZCommon-XYZWidgetExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZCommon-XYZWidgetExtension.debug.xcconfig"; path = "Target Support Files/Pods-XYZCommon-XYZWidgetExtension/Pods-XYZCommon-XYZWidgetExtension.debug.xcconfig"; sourceTree = "<group>"; };
		F1C5DE250E262740C870BDCE /* Pods-XYZ.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-XYZ.debug.xcconfig"; path = "Target Support Files/Pods-XYZ/Pods-XYZ.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		E4B2A9E92E13D76900CD6B08 /* Exceptions for "XYZWidget" folder in "XYZWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E4B2A9CE2E13D76700CD6B08 /* XYZWidgetExtension */;
		};
		E4B2A9F42E13E21C00CD6B08 /* Exceptions for "XYZ" folder in "XYZWidgetExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Models/JournalEntry.swift,
				Others/Foundation/XYLog.swift,
			);
			target = E4B2A9CE2E13D76700CD6B08 /* XYZWidgetExtension */;
		};
		E4EBBD332D2D0C83006D1114 /* Exceptions for "XYZ" folder in "XYZ" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
				Resources/API_Debug.json,
				Resources/API_Prod.json,
			);
			target = E4EBBD0A2D2D0C81006D1114 /* XYZ */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E4B2A9D42E13D76700CD6B08 /* XYZWidget */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E4B2A9E92E13D76900CD6B08 /* Exceptions for "XYZWidget" folder in "XYZWidgetExtension" target */,
			);
			path = XYZWidget;
			sourceTree = "<group>";
		};
		E4EBBD0D2D2D0C81006D1114 /* XYZ */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E4EBBD332D2D0C83006D1114 /* Exceptions for "XYZ" folder in "XYZ" target */,
				E4B2A9F42E13E21C00CD6B08 /* Exceptions for "XYZ" folder in "XYZWidgetExtension" target */,
			);
			path = XYZ;
			sourceTree = "<group>";
		};
		E4EBBD242D2D0C83006D1114 /* XYZTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = XYZTests;
			sourceTree = "<group>";
		};
		E4EBBD2E2D2D0C83006D1114 /* XYZUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = XYZUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E4B2A9CC2E13D76700CD6B08 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E4B2A9D32E13D76700CD6B08 /* SwiftUI.framework in Frameworks */,
				E4B2A9D12E13D76700CD6B08 /* WidgetKit.framework in Frameworks */,
				44D95136D4B6170DE7787FF6 /* Pods_XYZCommon_XYZWidgetExtension.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD082D2D0C81006D1114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E440EF032D31793C00E18AB0 /* CombineCocoa in Frameworks */,
				E440EF062D317A6600E18AB0 /* CombineCocoa in Frameworks */,
				E4B919062D6EC20C004C6B74 /* StoreKit.framework in Frameworks */,
				FB1C60BBD07AF1E3274D90BD /* Pods_XYZCommon_XYZ.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD1E2D2D0C83006D1114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				021480F53F6A432EE8F84261 /* Pods_XYZTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD282D2D0C83006D1114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9EC49C4EC383030ECDC83329 /* Pods_XYZCommon_XYZUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9FE23E75A4D4C711FB4DBDA7 /* Pods */ = {
			isa = PBXGroup;
			children = (
				F1C5DE250E262740C870BDCE /* Pods-XYZ.debug.xcconfig */,
				4037953381C25A069E20478C /* Pods-XYZ.release.xcconfig */,
				DBF9FC5589682C2C0434284C /* Pods-XYZ-XYZUITests.debug.xcconfig */,
				20BA8106D7C1D1F825BDF821 /* Pods-XYZ-XYZUITests.release.xcconfig */,
				B0308D3F9A9626CEB015148A /* Pods-XYZTests.debug.xcconfig */,
				889332147A441AA0830EC34F /* Pods-XYZTests.release.xcconfig */,
				7C565C77790DADE92FDF775D /* Pods-XYZ-XYZWidgetExtension.debug.xcconfig */,
				174FC7DDE0E41C7C90687C57 /* Pods-XYZ-XYZWidgetExtension.release.xcconfig */,
				218D7D3F0BDE0A3F3FB30B62 /* Pods-XYZCommon-XYZ.debug.xcconfig */,
				99A3E6C6B1E4F1802BBB2D3C /* Pods-XYZCommon-XYZ.release.xcconfig */,
				14249290A6E0D5B41720DF05 /* Pods-XYZCommon-XYZUITests.debug.xcconfig */,
				15A04F289A01ED426EC4CE4F /* Pods-XYZCommon-XYZUITests.release.xcconfig */,
				E95C01900ED6E58B370920D8 /* Pods-XYZCommon-XYZWidgetExtension.debug.xcconfig */,
				95EE5AC903D73E1B01C6281B /* Pods-XYZCommon-XYZWidgetExtension.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E4EBBD022D2D0C81006D1114 = {
			isa = PBXGroup;
			children = (
				E4EBBD0D2D2D0C81006D1114 /* XYZ */,
				E4EBBD242D2D0C83006D1114 /* XYZTests */,
				E4EBBD2E2D2D0C83006D1114 /* XYZUITests */,
				E4B2A9D42E13D76700CD6B08 /* XYZWidget */,
				E4EBBD0C2D2D0C81006D1114 /* Products */,
				9FE23E75A4D4C711FB4DBDA7 /* Pods */,
				F17019646245ABD470B7B66C /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		E4EBBD0C2D2D0C81006D1114 /* Products */ = {
			isa = PBXGroup;
			children = (
				E4EBBD0B2D2D0C81006D1114 /* XYZ.app */,
				E4EBBD212D2D0C83006D1114 /* XYZTests.xctest */,
				E4EBBD2B2D2D0C83006D1114 /* XYZUITests.xctest */,
				E4B2A9CF2E13D76700CD6B08 /* XYZWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F17019646245ABD470B7B66C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4B919052D6EC20C004C6B74 /* StoreKit.framework */,
				51EF4B94A8D9A973204E0320 /* Pods_XYZTests.framework */,
				E4B2A9D02E13D76700CD6B08 /* WidgetKit.framework */,
				E4B2A9D22E13D76700CD6B08 /* SwiftUI.framework */,
				0450BDBDD4B9ABC1BEDB6252 /* Pods_XYZCommon_XYZ.framework */,
				895923F3BB0B06350EA49B33 /* Pods_XYZCommon_XYZUITests.framework */,
				06C5B1137F4868071034C0D5 /* Pods_XYZCommon_XYZWidgetExtension.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E4B2A9CE2E13D76700CD6B08 /* XYZWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4B2A9E82E13D76900CD6B08 /* Build configuration list for PBXNativeTarget "XYZWidgetExtension" */;
			buildPhases = (
				E27CB1D644350E7E626BB466 /* [CP] Check Pods Manifest.lock */,
				E4B2A9CB2E13D76700CD6B08 /* Sources */,
				E4B2A9CC2E13D76700CD6B08 /* Frameworks */,
				E4B2A9CD2E13D76700CD6B08 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E4B2A9D42E13D76700CD6B08 /* XYZWidget */,
			);
			name = XYZWidgetExtension;
			productName = XYZWidgetExtension;
			productReference = E4B2A9CF2E13D76700CD6B08 /* XYZWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		E4EBBD0A2D2D0C81006D1114 /* XYZ */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4EBBD342D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZ" */;
			buildPhases = (
				245E472CE2339EC3972C5693 /* [CP] Check Pods Manifest.lock */,
				E440F0982D4151E200E18AB0 /* Code Gen */,
				E4EBBD072D2D0C81006D1114 /* Sources */,
				E4EBBD082D2D0C81006D1114 /* Frameworks */,
				E4EBBD092D2D0C81006D1114 /* Resources */,
				1E90B1AF74A25B17F6A02086 /* [CP] Embed Pods Frameworks */,
				0F4D7281BC9F4374DE777604 /* [CP] Copy Pods Resources */,
				E4B2A9E52E13D76900CD6B08 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				E4B2A9E32E13D76900CD6B08 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4EBBD0D2D2D0C81006D1114 /* XYZ */,
			);
			name = XYZ;
			productName = XYZ;
			productReference = E4EBBD0B2D2D0C81006D1114 /* XYZ.app */;
			productType = "com.apple.product-type.application";
		};
		E4EBBD202D2D0C83006D1114 /* XYZTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4EBBD392D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZTests" */;
			buildPhases = (
				3211BD2A760DF6F0EFF8633B /* [CP] Check Pods Manifest.lock */,
				E4EBBD1D2D2D0C83006D1114 /* Sources */,
				E4EBBD1E2D2D0C83006D1114 /* Frameworks */,
				E4EBBD1F2D2D0C83006D1114 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E4EBBD232D2D0C83006D1114 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4EBBD242D2D0C83006D1114 /* XYZTests */,
			);
			name = XYZTests;
			productName = XYZTests;
			productReference = E4EBBD212D2D0C83006D1114 /* XYZTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E4EBBD2A2D2D0C83006D1114 /* XYZUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4EBBD3C2D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZUITests" */;
			buildPhases = (
				EF3CBF23E90FB6E4DBEFDFAF /* [CP] Check Pods Manifest.lock */,
				E4EBBD272D2D0C83006D1114 /* Sources */,
				E4EBBD282D2D0C83006D1114 /* Frameworks */,
				E4EBBD292D2D0C83006D1114 /* Resources */,
				5571BEC06703DD9AC94EBFCD /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				E4EBBD2D2D2D0C83006D1114 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4EBBD2E2D2D0C83006D1114 /* XYZUITests */,
			);
			name = XYZUITests;
			productName = XYZUITests;
			productReference = E4EBBD2B2D2D0C83006D1114 /* XYZUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E4EBBD032D2D0C81006D1114 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					E4B2A9CE2E13D76700CD6B08 = {
						CreatedOnToolsVersion = 16.3;
					};
					E4EBBD0A2D2D0C81006D1114 = {
						CreatedOnToolsVersion = 16.2;
					};
					E4EBBD202D2D0C83006D1114 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = E4EBBD0A2D2D0C81006D1114;
					};
					E4EBBD2A2D2D0C83006D1114 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = E4EBBD0A2D2D0C81006D1114;
					};
				};
			};
			buildConfigurationList = E4EBBD062D2D0C81006D1114 /* Build configuration list for PBXProject "XYZ" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-cn",
				"zh-CN",
			);
			mainGroup = E4EBBD022D2D0C81006D1114;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				E4E90ABA2D3178D100F9A6CD /* XCRemoteSwiftPackageReference "CombineCocoa" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = E4EBBD0C2D2D0C81006D1114 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E4EBBD0A2D2D0C81006D1114 /* XYZ */,
				E4B2A9CE2E13D76700CD6B08 /* XYZWidgetExtension */,
				E4EBBD202D2D0C83006D1114 /* XYZTests */,
				E4EBBD2A2D2D0C83006D1114 /* XYZUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E4B2A9CD2E13D76700CD6B08 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD092D2D0C81006D1114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD1F2D2D0C83006D1114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD292D2D0C83006D1114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0F4D7281BC9F4374DE777604 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		1E90B1AF74A25B17F6A02086 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZ/Pods-XYZCommon-XYZ-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		245E472CE2339EC3972C5693 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XYZCommon-XYZ-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3211BD2A760DF6F0EFF8633B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XYZTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5571BEC06703DD9AC94EBFCD /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZUITests/Pods-XYZCommon-XYZUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZUITests/Pods-XYZCommon-XYZUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-XYZCommon-XYZUITests/Pods-XYZCommon-XYZUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E27CB1D644350E7E626BB466 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XYZCommon-XYZWidgetExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E440F0982D4151E200E18AB0 /* Code Gen */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Code Gen";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\nif [ \"$CONFIGURATION\" == \"Debug\" ]; then\n  INPUT_FILE=\"${SRCROOT}/XYZ/Resources/API_Debug.json\"\nelse\n  INPUT_FILE=\"${SRCROOT}/XYZ/Resources/API_Prod.json\"\nfi\n\n\nOUTPUT_FILE=\"${SRCROOT}/XYZ/Others/Foundation/JSON.swift\"\nmkdir -p \"$(dirname \"$OUTPUT_FILE\")\"\n\necho \"// Auto-generated from API.json\" > \"$OUTPUT_FILE\"\necho \"import Foundation\" >> \"$OUTPUT_FILE\"\necho \"enum JSONFiles {\" >> \"$OUTPUT_FILE\"\n\nto_camel_case() {\n  echo \"$1\" | awk -F'_' '{\n    for (i = 1; i <= NF; i++) {\n      if (i == 1) {\n        printf \"%s\", tolower($i)\n      } else {\n        printf \"%s\", toupper(substr($i,1,1)) substr($i,2)\n      }\n    }\n    print \"\"\n  }'\n}\n\njq -r 'to_entries[] | \"\\(.key)\\t\\(.value)\"' \"$INPUT_FILE\" | while IFS=$'\\t' read key value; do\n  camelKey=$(to_camel_case \"$key\")\n  echo \"    static let $camelKey = \\\"${value}\\\"\" >> \"$OUTPUT_FILE\"\ndone\n\necho \"}\" >> \"$OUTPUT_FILE\"\n";
		};
		EF3CBF23E90FB6E4DBEFDFAF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-XYZCommon-XYZUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E4B2A9CB2E13D76700CD6B08 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD072D2D0C81006D1114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD1D2D2D0C83006D1114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4EBBD272D2D0C83006D1114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E4B2A9E32E13D76900CD6B08 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4B2A9CE2E13D76700CD6B08 /* XYZWidgetExtension */;
			targetProxy = E4B2A9E22E13D76900CD6B08 /* PBXContainerItemProxy */;
		};
		E4EBBD232D2D0C83006D1114 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4EBBD0A2D2D0C81006D1114 /* XYZ */;
			targetProxy = E4EBBD222D2D0C83006D1114 /* PBXContainerItemProxy */;
		};
		E4EBBD2D2D2D0C83006D1114 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4EBBD0A2D2D0C81006D1114 /* XYZ */;
			targetProxy = E4EBBD2C2D2D0C83006D1114 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E4B2A9E62E13D76900CD6B08 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E95C01900ED6E58B370920D8 /* Pods-XYZCommon-XYZWidgetExtension.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = XYZWidget/XYZWidgetExtensionDebug.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XYZWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = XYZWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.journal.widget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Journal Widget";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E4B2A9E72E13D76900CD6B08 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 95EE5AC903D73E1B01C6281B /* Pods-XYZCommon-XYZWidgetExtension.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = XYZWidget/XYZWidgetExtensionRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XYZWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = XYZWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.journal.widget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Journal Distribution Widget";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E4EBBD352D2D0C83006D1114 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 218D7D3F0BDE0A3F3FB30B62 /* Pods-XYZCommon-XYZ.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = XYZ/Resources/XYZ.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XYZ/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "意日记";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSCameraUsageDescription = "请启用相机以便为您的日记添加配图";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "请允许访问Face ID以便为您的日记添加生物识别锁";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "请允许访问位置信息以便为您的日记添加位置标记";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "请允许访问位置信息以便为您的日记添加位置标记";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "请启用麦克风以便为您的日记记录音频";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.journal;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Journal Dev";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		E4EBBD362D2D0C83006D1114 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 99A3E6C6B1E4F1802BBB2D3C /* Pods-XYZCommon-XYZ.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "XYZ/Resources/XYZ-Release.entitlements";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XYZ/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "意日记";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSCameraUsageDescription = "请启用相机以便为您的日记添加配图";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "请允许访问Face ID以便为您的日记添加生物识别锁";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "请允许访问位置信息以便为您的日记添加位置标记";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "请允许访问位置信息以便为您的日记添加位置标记";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "请启用麦克风以便为您的日记记录音频";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.journal;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Journal Distribution";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		E4EBBD372D2D0C83006D1114 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E4EBBD382D2D0C83006D1114 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E4EBBD3A2D2D0C83006D1114 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B0308D3F9A9626CEB015148A /* Pods-XYZTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.XYZTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/XYZ.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/XYZ";
			};
			name = Debug;
		};
		E4EBBD3B2D2D0C83006D1114 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 889332147A441AA0830EC34F /* Pods-XYZTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.XYZTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/XYZ.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/XYZ";
			};
			name = Release;
		};
		E4EBBD3D2D2D0C83006D1114 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 14249290A6E0D5B41720DF05 /* Pods-XYZCommon-XYZUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.XYZUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = XYZ;
			};
			name = Debug;
		};
		E4EBBD3E2D2D0C83006D1114 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 15A04F289A01ED426EC4CE4F /* Pods-XYZCommon-XYZUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.XYZUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = XYZ;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E4B2A9E82E13D76900CD6B08 /* Build configuration list for PBXNativeTarget "XYZWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4B2A9E62E13D76900CD6B08 /* Debug */,
				E4B2A9E72E13D76900CD6B08 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4EBBD062D2D0C81006D1114 /* Build configuration list for PBXProject "XYZ" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4EBBD372D2D0C83006D1114 /* Debug */,
				E4EBBD382D2D0C83006D1114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4EBBD342D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZ" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4EBBD352D2D0C83006D1114 /* Debug */,
				E4EBBD362D2D0C83006D1114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4EBBD392D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4EBBD3A2D2D0C83006D1114 /* Debug */,
				E4EBBD3B2D2D0C83006D1114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4EBBD3C2D2D0C83006D1114 /* Build configuration list for PBXNativeTarget "XYZUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4EBBD3D2D2D0C83006D1114 /* Debug */,
				E4EBBD3E2D2D0C83006D1114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		E4E90ABA2D3178D100F9A6CD /* XCRemoteSwiftPackageReference "CombineCocoa" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/CombineCommunity/CombineCocoa";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.4.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		E440EF022D31793C00E18AB0 /* CombineCocoa */ = {
			isa = XCSwiftPackageProductDependency;
			package = E4E90ABA2D3178D100F9A6CD /* XCRemoteSwiftPackageReference "CombineCocoa" */;
			productName = CombineCocoa;
		};
		E440EF052D317A6600E18AB0 /* CombineCocoa */ = {
			isa = XCSwiftPackageProductDependency;
			package = E4E90ABA2D3178D100F9A6CD /* XCRemoteSwiftPackageReference "CombineCocoa" */;
			productName = CombineCocoa;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = E4EBBD032D2D0C81006D1114 /* Project object */;
}
