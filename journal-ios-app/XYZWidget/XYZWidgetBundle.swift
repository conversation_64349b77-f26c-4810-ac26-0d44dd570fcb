//
//  XYZWidgetBundle.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import WidgetKit
import SwiftUI

@main
struct XYZWidgetBundle: WidgetBundle {
    var body: some Widget {
        XYZWidget()

        // iOS 18+ only features
        if #available(iOS 18.0, *) {
            XYZWidgetControl()
        }

        // Live Activities (iOS 16.1+, but some features require iOS 17+)
        if #available(iOS 16.1, *) {
            XYZWidgetLiveActivity()
        }
    }
}
