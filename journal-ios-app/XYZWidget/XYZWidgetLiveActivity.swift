//
//  XYZWidgetLiveActivity.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import ActivityKit
import WidgetKit
import Swift<PERSON>

@available(iOS 16.1, *)
struct XYZWidgetAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

@available(iOS 16.1, *)
struct XYZWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: XYZWidgetAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

extension XYZWidgetAttributes {
    fileprivate static var preview: XYZWidgetAttributes {
        XYZWidgetAttributes(name: "World")
    }
}

extension XYZWidgetAttributes.ContentState {
    fileprivate static var smiley: XYZWidgetAttributes.ContentState {
        XYZWidgetAttributes.ContentState(emoji: "😀")
     }
     
     fileprivate static var starEyes: XYZWidgetAttributes.ContentState {
         XYZWidgetAttributes.ContentState(emoji: "🤩")
     }
}

#Preview("Notification", as: .content, using: XYZWidgetAttributes.preview) {
   XYZWidgetLiveActivity()
} contentStates: {
    XYZWidgetAttributes.ContentState.smiley
    XYZWidgetAttributes.ContentState.starEyes
}
