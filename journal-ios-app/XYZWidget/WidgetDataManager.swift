//
//  WidgetDataManager.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation

class WidgetDataManager {
    static let shared = WidgetDataManager()
    
    private let appGroupIdentifier = "group.cn.seungyu.journal"
    private var userDefaults: UserDefaults?
    
    private init() {
        userDefaults = UserDefaults(suiteName: appGroupIdentifier)
    }

    var lastWritingDate: Date? {
        get {
            return userDefaults?.object(forKey: "last_writing_date") as? Date
        }
        set {
            userDefaults?.set(newValue, forKey: "last_writing_date")
        }
    }
    
    var totalEntries: Int {
        get {
            return userDefaults?.integer(forKey: "total_entries") ?? 0
        }
        set {
            userDefaults?.set(newValue, forKey: "total_entries")
        }
    }

    // MARK: - Motivational Messages
    
    func getMotivationalMessage() -> String {
        let messages = [
            "Your thoughts matter! 💭",
            "Every story starts with a single word ✍️",
            "Capture this moment 📸",
            "What's on your mind? 🤔",
            "Your future self will thank you 🙏",
            "Make today memorable 🌟",
            "Small moments, big memories 💫",
            "Write your heart out ❤️",
            "Today's adventure awaits 🗺️",
            "Express yourself freely 🎨"
        ]
        
        let today = Date()
        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: today) ?? 1
        let index = dayOfYear % messages.count
        return messages[index]
    }
    
    // MARK: - Recent Activity
    
    func hasWrittenToday() -> Bool {
        guard let lastDate = lastWritingDate else { return false }
        return Calendar.current.isDateInToday(lastDate)
    }
    
    func getDaysUntilStreakBreak() -> Int {
        guard let lastDate = lastWritingDate else { return 0 }
        let today = Calendar.current.startOfDay(for: Date())
        let lastWritingDay = Calendar.current.startOfDay(for: lastDate)
        let daysBetween = Calendar.current.dateComponents([.day], from: lastWritingDay, to: today).day ?? 0
        
        if daysBetween >= 2 {
            return 0 // Streak already broken
        } else if daysBetween == 1 {
            return 0 // Today is the last day to maintain streak
        } else {
            return 1 // Still have today
        }
    }
    
    // MARK: - Widget Display Data

    func getWidgetDisplayData() -> WidgetDisplayData {
        let todaysEntry = getTodaysJournalEntry()
        let isNewUser = totalEntries == 0
        let timeOfDay = TimeOfDay.current()

        return WidgetDisplayData(
            motivationalMessage: getMotivationalMessage(),
            hasWrittenToday: hasWrittenToday(),
            totalEntries: totalEntries,
            todaysJournalEntry: todaysEntry,
            isNewUser: isNewUser,
            timeOfDay: timeOfDay
        )
    }

    // MARK: - Journal Entry Access

    private func getTodaysJournalEntry() -> WidgetJournalEntry? {
        let todaysEntries = SharedDataManager.shared.getEntriesForDate(Date())
        return todaysEntries.first // Get the most recent entry for today
    }
}

// MARK: - Data Models

enum TimeOfDay {
    case morning    // 6 AM - 6 PM
    case evening    // 6 PM - 6 AM

    static func current() -> TimeOfDay {
        let hour = Calendar.current.component(.hour, from: Date())
        return (hour >= 6 && hour < 18) ? .morning : .evening
    }
}

struct WidgetDisplayData {
    let motivationalMessage: String
    let hasWrittenToday: Bool
    let totalEntries: Int
    let todaysJournalEntry: WidgetJournalEntry?
    let isNewUser: Bool
    let timeOfDay: TimeOfDay
}


